<?php

  AppModel::loadModelClass('IndufastWorkdaySummaryModel');

class IndufastWorkdaySummary extends IndufastWorkdaySummaryModel {

  use ModelFillTrait;
  use ModelTimeTrait;
  use ValidationTrait;
  use PropertyCastTrait;

  public IndufastEmployee $employee;

  protected array $fillable = [
    'year' => 'required|integer',
    'month' => 'required|integer|min:1|max:12',
    'employee_id' => 'required|integer|exists:indufast_employee,id',
    'data' => 'required_if:locked,1',
  ];

  public static function setMonthlyBalance(int $year, int $month, int $employeeId, string $monthlyBalance): void {
    $summary = self::find_by(['year' => $year, 'month' => $month, 'employee_id' => $employeeId]);
    if (!$summary) {
      $summary = new self();
      $summary->year = $year;
      $summary->month = $month;
      $summary->employee_id = $employeeId;
    }
    $summary->monthly_balance = $monthlyBalance;
    $summary->save();
  }
}